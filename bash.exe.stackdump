Stack trace:
Frame         Function      Args
0007FFFF53F0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFF53F0, 0007FFFF42F0) msys-2.0.dll+0x2118E
0007FFFF53F0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF53F0  0002100469F2 (00021028DF99, 0007FFFF52A8, 0007FFFF53F0, 000000000000) msys-2.0.dll+0x69F2
0007FFFF53F0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF53F0  00021006A545 (0007FFFF5400, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF5400, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE38570000 ntdll.dll
7FFE36CC0000 KERNEL32.DLL
7FFE36070000 KERNELBASE.dll
7FFE30B00000 apphelp.dll
7FFE37F50000 USER32.dll
7FFE35CD0000 win32u.dll
7FFE382A0000 GDI32.dll
7FFE35EA0000 gdi32full.dll
7FFE35D00000 msvcp_win.dll
7FFE35DA0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE375A0000 advapi32.dll
7FFE37A30000 msvcrt.dll
7FFE36D90000 sechost.dll
7FFE382D0000 RPCRT4.dll
7FFE36560000 bcrypt.dll
7FFE35580000 CRYPTBASE.DLL
7FFE36370000 bcryptPrimitives.dll
7FFE377E0000 IMM32.DLL
