{"name": "valueans", "version": "0.1.0", "private": true, "homepage": "https://Salman2410>.github.io/Updated_prod_frontend", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next export", "deploy": "gh-pages -b gh-pages -d out"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@heroicons/react": "^2.2.0", "@mui/material": "^7.1.2", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-slot": "^1.1.1", "@tailwindcss/line-clamp": "^0.4.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.5.2", "export": "^0.1.337", "lucide-react": "^0.462.0", "next": "14.2.13", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.5.2", "react-html-parser": "^2.0.2", "react-icons": "^5.4.0", "react-slick": "^0.30.2", "react-spring": "^9.7.4", "sharp": "^0.33.5", "slick-carousel": "^1.8.1", "swiper": "^11.2.4", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.2.13", "gh-pages": "^6.2.0", "postcss": "^8", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "tailwindcss": "^3.4.13"}}