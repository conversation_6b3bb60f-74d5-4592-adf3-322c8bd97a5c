"use client";
import Image from "next/image";
import React, { useState, useEffect } from "react";
import Link from "next/link";

const Section1 = () => {
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch blogs from API
  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        setLoading(true);
        const response = await fetch("https://api.valueans.com/api/blogs/");

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const blogsData = data.results ? data.results.slice(0, 5) : [];
        setBlogs(blogsData);
      } catch (err) {
        console.error("Error fetching blogs:", err);
        // Use fallback data if API fails
        setBlogs([
          {
            id: 1,
            title: "Transformative Machine Learning Solutions Development",
            image: "/Images/ML_banner.png",
            created_at: "2025-05-20",
            slug_field: "transformative-ml-solutions"
          },
          {
            id: 2,
            title: "AI-Powered Business Solutions for Modern Enterprises",
            image: "/Images/ML_banner.png",
            created_at: "2025-05-18",
            slug_field: "ai-powered-business-solutions"
          },
          {
            id: 3,
            title: "The Future of Software Development with AI",
            image: "/Images/ML_banner.png",
            created_at: "2025-05-15",
            slug_field: "future-software-development-ai"
          },
          {
            id: 4,
            title: "Custom Software Solutions for Healthcare Industry",
            image: "/Images/ML_banner.png",
            created_at: "2025-05-12",
            slug_field: "custom-software-healthcare"
          },
          {
            id: 5,
            title: "Digital Transformation Strategies for 2025",
            image: "/Images/ML_banner.png",
            created_at: "2025-05-10",
            slug_field: "digital-transformation-2025"
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchBlogs();
  }, []);

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Truncate title
  const truncateTitle = (title, maxLength = 60) => {
    if (title.length <= maxLength) return title;
    return title.substring(0, maxLength) + '...';
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="flex flex-col lg:flex-row gap-6">
            <div className="w-full lg:w-[60%]">
              <div className="bg-gray-300 h-64 md:h-80 rounded-lg mb-4"></div>
              <div className="bg-gray-300 h-8 rounded mb-2"></div>
              <div className="bg-gray-300 h-4 rounded w-32"></div>
            </div>
            <div className="w-full lg:w-[40%] space-y-4">
              {[1, 2, 3, 4].map((item) => (
                <div key={item} className="flex gap-3">
                  <div className="bg-gray-300 w-20 h-20 rounded"></div>
                  <div className="flex-1">
                    <div className="bg-gray-300 h-4 rounded mb-2"></div>
                    <div className="bg-gray-300 h-3 rounded w-20"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  const featuredBlog = blogs[0];
  const sidebarBlogs = blogs.slice(1, 5);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
        {/* Featured Blog - Left Side */}
        <div className="w-full lg:w-[60%]">
          {featuredBlog && (
            <Link href={`/blog/${featuredBlog.slug_field}`} className="block group">
              <div className="relative overflow-hidden rounded-lg mb-4">
                <Image
                  src={featuredBlog.image || "/Images/ML_banner.png"}
                  alt={featuredBlog.title}
                  width={600}
                  height={400}
                  className="w-full h-64 md:h-80 object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300"></div>
              </div>
              <h2 className="text-xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 group-hover:text-[#F245A1] transition-colors duration-300">
                {featuredBlog.title}
              </h2>
              <p className="text-gray-600 text-sm md:text-base">
                {formatDate(featuredBlog.created_at)}
              </p>
            </Link>
          )}
        </div>

        {/* Sidebar Blogs - Right Side */}
        <div className="w-full lg:w-[40%]">
          <div className="space-y-4">
            {sidebarBlogs.map((blog, index) => (
              <Link
                key={blog.id || index}
                href={`/blog/${blog.slug_field}`}
                className="flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-300 group"
              >
                <div className="flex-shrink-0">
                  <Image
                    src={blog.image || "/Images/ML_banner.png"}
                    alt={blog.title}
                    width={80}
                    height={80}
                    className="w-16 h-16 md:w-20 md:h-20 object-cover rounded-lg"
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm md:text-base font-semibold text-gray-900 mb-2 group-hover:text-[#F245A1] transition-colors duration-300 leading-tight">
                    {truncateTitle(blog.title, 80)}
                  </h3>
                  <p className="text-xs md:text-sm text-gray-600">
                    {formatDate(blog.created_at)}
                  </p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Section1;
