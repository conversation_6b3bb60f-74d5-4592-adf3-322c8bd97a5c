"use client";
import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";

const Section2 = () => {
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);
  const carouselRef = useRef(null);

  // Fetch blogs from API
  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        setLoading(true);
        const response = await fetch("https://api.valueans.com/api/blogs/");

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        // Get blogs 5-11 for featured section (different from Section 1)
        const blogsData = data.results ? data.results.slice(5, 12) : [];
        setBlogs(blogsData);
      } catch (err) {
        console.error("Error fetching blogs:", err);
        // Use fallback data if API fails
        setBlogs([
          {
            id: 6,
            title: "Advanced Web Development Techniques",
            image: "/Images/ML_banner.png",
            created_at: "2025-05-08",
            slug_field: "advanced-web-development",
            content: "Explore the latest web development techniques and best practices for modern applications."
          },
          {
            id: 7,
            title: "Mobile App Development Trends 2025",
            image: "/Images/ML_banner.png",
            created_at: "2025-05-06",
            slug_field: "mobile-app-trends-2025",
            content: "Discover the emerging trends in mobile app development for the year 2025."
          },
          {
            id: 8,
            title: "Cloud Computing Solutions for Startups",
            image: "/Images/ML_banner.png",
            created_at: "2025-05-04",
            slug_field: "cloud-computing-startups",
            content: "Learn how startups can leverage cloud computing for scalable solutions."
          },
          {
            id: 9,
            title: "Cybersecurity Best Practices",
            image: "/Images/ML_banner.png",
            created_at: "2025-05-02",
            slug_field: "cybersecurity-best-practices",
            content: "Essential cybersecurity practices every business should implement."
          },
          {
            id: 10,
            title: "Data Analytics for Business Growth",
            image: "/Images/ML_banner.png",
            created_at: "2025-04-30",
            slug_field: "data-analytics-business-growth",
            content: "How data analytics can drive business growth and decision making."
          },
          {
            id: 11,
            title: "IoT Solutions for Smart Cities",
            image: "/Images/ML_banner.png",
            created_at: "2025-04-28",
            slug_field: "iot-smart-cities",
            content: "Exploring IoT implementations in smart city infrastructure."
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchBlogs();
  }, []);

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Truncate text
  const truncateText = (text, maxLength = 100) => {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // Navigation functions
  const scrollLeft = () => {
    if (carouselRef.current) {
      const cardWidth = carouselRef.current.children[0]?.offsetWidth + 24; // card width + gap
      carouselRef.current.scrollBy({ left: -cardWidth, behavior: 'smooth' });
      setCurrentIndex(Math.max(0, currentIndex - 1));
    }
  };

  const scrollRight = () => {
    if (carouselRef.current) {
      const cardWidth = carouselRef.current.children[0]?.offsetWidth + 24; // card width + gap
      carouselRef.current.scrollBy({ left: cardWidth, behavior: 'smooth' });
      setCurrentIndex(Math.min(blogs.length - 3, currentIndex + 1));
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="flex justify-between items-center mb-6">
            <div className="bg-gray-300 h-8 w-32 rounded"></div>
            <div className="flex gap-2">
              <div className="bg-gray-300 h-10 w-10 rounded"></div>
              <div className="bg-gray-300 h-10 w-10 rounded"></div>
            </div>
          </div>
          <div className="flex gap-6">
            {[1, 2, 3].map((item) => (
              <div key={item} className="flex-1">
                <div className="bg-gray-300 h-48 rounded-lg mb-4"></div>
                <div className="bg-gray-300 h-6 rounded mb-2"></div>
                <div className="bg-gray-300 h-4 rounded w-24"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header with Navigation */}
      <div className="flex justify-between items-center mb-8">
        <h3 className="text-2xl md:text-3xl font-bold text-gray-900">Featured</h3>
        <div className="flex gap-2">
          <button
            onClick={scrollLeft}
            disabled={currentIndex === 0}
            className="p-2 rounded-full border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
          >
            <ChevronLeftIcon className="w-5 h-5 text-gray-600" />
          </button>
          <button
            onClick={scrollRight}
            disabled={currentIndex >= blogs.length - 3}
            className="p-2 rounded-full border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
          >
            <ChevronRightIcon className="w-5 h-5 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Carousel */}
      <div className="relative overflow-hidden">
        <div
          ref={carouselRef}
          className="flex gap-6 overflow-x-auto scrollbar-hide scroll-smooth"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {blogs.map((blog, index) => (
            <div key={blog.id || index} className="flex-shrink-0 w-full sm:w-80 md:w-96">
              <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden group">
                <Link href={`/blog/${blog.slug_field}`}>
                  <div className="relative overflow-hidden">
                    <Image
                      src={blog.image || "/Images/ML_banner.png"}
                      alt={blog.title}
                      width={400}
                      height={250}
                      className="w-full h-48 md:h-56 object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>
                  </div>
                  <div className="p-6">
                    <h3 className="text-lg md:text-xl font-semibold text-gray-900 mb-3 group-hover:text-[#F245A1] transition-colors duration-300 line-clamp-2">
                      {blog.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {truncateText(blog.content)}
                    </p>
                    <div className="flex justify-between items-center">
                      <p className="text-gray-500 text-sm">
                        {formatDate(blog.created_at)}
                      </p>
                      <span className="text-[#F245A1] font-medium text-sm group-hover:underline">
                        Read More
                      </span>
                    </div>
                  </div>
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Section2;
