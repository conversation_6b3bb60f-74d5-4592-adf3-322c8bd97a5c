"use client";
import React, { useState } from 'react';
import { MagnifyingGlassIcon, ChevronDownIcon } from '@heroicons/react/24/outline';

const Section3 = ({ onSearch, onFilterChange, searchTerm, filters }) => {
  const [isServicesOpen, setIsServicesOpen] = useState(false);
  const [isIndustriesOpen, setIsIndustriesOpen] = useState(false);
  const [isTechnologiesOpen, setIsTechnologiesOpen] = useState(false);

  // Services data from the codebase
  const services = [
    "Custom Software Development",
    "Mobile App Development",
    "Web Development",
    "AI/ML Solutions",
    "Digital Transformation",
    "SaaS Development",
    "Fintech Solutions",
    "Healthcare Software",
    "Software Development",
    "IT Consulting",
    "Application Services",
    "Testing & QA",
    "Data Analytics",
    "Help Desk Services",
    "Infrastructure Services",
    "Cybersecurity Services",
    "Quality Assurance and Testing",
    "Business Intelligence",
    "Data Engineering",
    "Cloud Services",
    "Generative AI",
    "UI/UX Design",
    "System Integration"
  ];

  // Industries data from the codebase
  const industries = [
    "Travel/Hospitality",
    "Healthcare",
    "Fintech",
    "E-commerce",
    "Education/E-Learning",
    "Real Estate",
    "Social Networking",
    "Gaming",
    "Entertainment",
    "Telecom",
    "Construction",
    "Manufacturing",
    "Agriculture",
    "Oil and Gas",
    "Logistics",
    "Banking",
    "Retail",
    "Legal",
    "Software"
  ];

  // Technologies data from the codebase
  const technologies = [
    "Code Deployment",
    "NLP",
    "AR & VR",
    "Microservices",
    "IoT",
    "SaaS",
    "AI As A Service",
    "Automated Machine Learning",
    "Predictive Analytics",
    "Progressive Web Apps",
    "Cross-platform & Hybrid Development",
    "React Native",
    "Flutter",
    "Node.js",
    "Python",
    "Java",
    "Cloud Computing",
    "Blockchain",
    "DevOps"
  ];

  const handleSearchChange = (e) => {
    if (onSearch) {
      onSearch(e.target.value);
    }
  };

  const handleFilterSelect = (filterType, value) => {
    if (onFilterChange) {
      onFilterChange(filterType, value);
    }
    // Close dropdowns after selection
    setIsServicesOpen(false);
    setIsIndustriesOpen(false);
    setIsTechnologiesOpen(false);
  };

  const clearAllFilters = () => {
    if (onFilterChange) {
      onFilterChange('clear', '');
    }
    if (onSearch) {
      onSearch('');
    }
  };

  return (
    <div className='bg-[#794CEC1A] py-8'>
      <div className='container mx-auto px-4'>
        {/* First Row - Search Bar Centered */}
        <div className='flex justify-center mb-6'>
          <div className='relative w-full max-w-md'>
            <div className='relative'>
              <MagnifyingGlassIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400' />
              <input
                type="search"
                placeholder='Search blogs and topics'
                value={searchTerm || ''}
                onChange={handleSearchChange}
                className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#7716BC] focus:border-transparent outline-none transition-all duration-300'
              />
            </div>
          </div>
        </div>

        {/* Second Row - All Categories and Filters */}
        <div className='flex flex-col sm:flex-row items-center justify-center gap-4'>
          {/* All Categories Link */}
          <button
            onClick={clearAllFilters}
            className='text-[#7716BC] font-medium hover:underline transition-all duration-300'
          >
            All Categories
          </button>

          <span className='text-gray-600 font-medium'>Filter By:</span>

          <div className='flex flex-wrap gap-3 justify-center'>
              {/* Services Dropdown */}
            <div className='relative'>
              <button
                onClick={() => setIsServicesOpen(!isServicesOpen)}
                className='flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:border-[#7716BC] focus:ring-2 focus:ring-[#7716BC] focus:border-transparent outline-none transition-all duration-300'
              >
                <span className='text-gray-700'>
                  {filters?.service || 'Services'}
                </span>
                <ChevronDownIcon className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${isServicesOpen ? 'rotate-180' : ''}`} />
              </button>

              {isServicesOpen && (
                <div className='absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto'>
                  {services.map((service, index) => (
                    <button
                      key={index}
                      onClick={() => handleFilterSelect('service', service)}
                      className='w-full text-left px-4 py-2 hover:bg-gray-50 hover:text-[#7716BC] transition-colors duration-200 text-sm'
                    >
                      {service}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Industries Dropdown */}
            <div className='relative'>
              <button
                onClick={() => setIsIndustriesOpen(!isIndustriesOpen)}
                className='flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:border-[#7716BC] focus:ring-2 focus:ring-[#7716BC] focus:border-transparent outline-none transition-all duration-300'
              >
                <span className='text-gray-700'>
                  {filters?.industry || 'Industry'}
                </span>
                <ChevronDownIcon className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${isIndustriesOpen ? 'rotate-180' : ''}`} />
              </button>

              {isIndustriesOpen && (
                <div className='absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto'>
                  {industries.map((industry, index) => (
                    <button
                      key={index}
                      onClick={() => handleFilterSelect('industry', industry)}
                      className='w-full text-left px-4 py-2 hover:bg-gray-50 hover:text-[#7716BC] transition-colors duration-200 text-sm'
                    >
                      {industry}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Technologies Dropdown */}
            <div className='relative'>
              <button
                onClick={() => setIsTechnologiesOpen(!isTechnologiesOpen)}
                className='flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:border-[#7716BC] focus:ring-2 focus:ring-[#7716BC] focus:border-transparent outline-none transition-all duration-300'
              >
                <span className='text-gray-700'>
                  {filters?.technology || 'Technology'}
                </span>
                <ChevronDownIcon className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${isTechnologiesOpen ? 'rotate-180' : ''}`} />
              </button>

              {isTechnologiesOpen && (
                <div className='absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto'>
                  {technologies.map((technology, index) => (
                    <button
                      key={index}
                      onClick={() => handleFilterSelect('technology', technology)}
                      className='w-full text-left px-4 py-2 hover:bg-gray-50 hover:text-[#7716BC] transition-colors duration-200 text-sm'
                    >
                      {technology}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {(filters?.service || filters?.industry || filters?.technology || searchTerm) && (
          <div className='mt-4 flex flex-wrap gap-2'>
            <span className='text-sm text-gray-600'>Active filters:</span>
            {searchTerm && (
              <span className='inline-flex items-center gap-1 px-3 py-1 bg-[#7716BC] text-white text-sm rounded-full'>
                Search: "{searchTerm}"
                <button onClick={() => onSearch('')} className='ml-1 hover:text-gray-200'>×</button>
              </span>
            )}
            {filters?.service && (
              <span className='inline-flex items-center gap-1 px-3 py-1 bg-[#7716BC] text-white text-sm rounded-full'>
                {filters.service}
                <button onClick={() => handleFilterSelect('service', '')} className='ml-1 hover:text-gray-200'>×</button>
              </span>
            )}
            {filters?.industry && (
              <span className='inline-flex items-center gap-1 px-3 py-1 bg-[#7716BC] text-white text-sm rounded-full'>
                {filters.industry}
                <button onClick={() => handleFilterSelect('industry', '')} className='ml-1 hover:text-gray-200'>×</button>
              </span>
            )}
            {filters?.technology && (
              <span className='inline-flex items-center gap-1 px-3 py-1 bg-[#7716BC] text-white text-sm rounded-full'>
                {filters.technology}
                <button onClick={() => handleFilterSelect('technology', '')} className='ml-1 hover:text-gray-200'>×</button>
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Section3;