"use client";
import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ChevronDownIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

const Section4 = ({ searchTerm, filters }) => {
  const [blogs, setBlogs] = useState([]);
  const [filteredBlogs, setFilteredBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState('newest');
  const [isSortOpen, setIsSortOpen] = useState(false);

  const blogsPerPage = 9;

  // Fetch blogs from API
  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        setLoading(true);
        const response = await fetch("https://api.valueans.com/api/blogs/");

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        // Get all blogs for the grid section
        const blogsData = data.results || [];
        setBlogs(blogsData);
        setFilteredBlogs(blogsData);
      } catch (err) {
        console.error("Error fetching blogs:", err);
        // Use fallback data if API fails
        const fallbackBlogs = Array.from({ length: 24 }, (_, i) => ({
          id: i + 1,
          title: `Blog Post ${i + 1}: Technology Insights and Solutions`,
          image: "/Images/ML_banner.png",
          created_at: new Date(2025, 4, 20 - i).toISOString(),
          slug_field: `blog-post-${i + 1}`,
          content: `This is a sample blog post about technology and software development. Post number ${i + 1}.`,
          categories: { name: i % 3 === 0 ? 'Technology' : i % 3 === 1 ? 'Business' : 'Development' }
        }));
        setBlogs(fallbackBlogs);
        setFilteredBlogs(fallbackBlogs);
      } finally {
        setLoading(false);
      }
    };

    fetchBlogs();
  }, []);

  // Filter and search blogs
  useEffect(() => {
    let filtered = [...blogs];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(blog =>
        blog.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (blog.content && blog.content.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply category filters (basic implementation - in real scenario, you'd have proper category fields)
    if (filters?.service) {
      filtered = filtered.filter(blog =>
        blog.title.toLowerCase().includes(filters.service.toLowerCase()) ||
        (blog.content && blog.content.toLowerCase().includes(filters.service.toLowerCase()))
      );
    }

    if (filters?.industry) {
      filtered = filtered.filter(blog =>
        blog.title.toLowerCase().includes(filters.industry.toLowerCase()) ||
        (blog.content && blog.content.toLowerCase().includes(filters.industry.toLowerCase()))
      );
    }

    if (filters?.technology) {
      filtered = filtered.filter(blog =>
        blog.title.toLowerCase().includes(filters.technology.toLowerCase()) ||
        (blog.content && blog.content.toLowerCase().includes(filters.technology.toLowerCase()))
      );
    }

    // Apply sorting
    if (sortBy === 'newest') {
      filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    } else if (sortBy === 'oldest') {
      filtered.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
    }

    setFilteredBlogs(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [blogs, searchTerm, filters, sortBy]);

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Truncate text
  const truncateText = (text, maxLength = 120) => {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // Pagination calculations
  const totalPages = Math.ceil(filteredBlogs.length / blogsPerPage);
  const startIndex = (currentPage - 1) * blogsPerPage;
  const endIndex = startIndex + blogsPerPage;
  const currentBlogs = filteredBlogs.slice(startIndex, endIndex);
  const totalResults = filteredBlogs.length;
  const showingStart = totalResults > 0 ? startIndex + 1 : 0;
  const showingEnd = Math.min(endIndex, totalResults);

  // Pagination handlers
  const goToPage = (page) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const goToPrevious = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  };

  const goToNext = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  };

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      if (currentPage <= 3) {
        for (let i = 1; i <= 4; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 2) {
        pages.push(1);
        pages.push('...');
        for (let i = totalPages - 3; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        pages.push(1);
        pages.push('...');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      }
    }

    return pages;
  };

  if (loading) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className="animate-pulse">
          <div className='flex justify-between items-center mb-8'>
            <div className="bg-gray-300 h-6 w-48 rounded"></div>
            <div className="bg-gray-300 h-10 w-32 rounded"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((item) => (
              <div key={item} className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="bg-gray-300 h-48"></div>
                <div className="p-6">
                  <div className="bg-gray-300 h-6 rounded mb-3"></div>
                  <div className="bg-gray-300 h-4 rounded mb-2"></div>
                  <div className="bg-gray-300 h-4 rounded w-24"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Results Header */}
      <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8'>
        <p className='text-gray-600'>
          Showing {showingStart}-{showingEnd} of {totalResults} results
        </p>

        {/* Sort Dropdown */}
        <div className='relative'>
          <button
            onClick={() => setIsSortOpen(!isSortOpen)}
            className='flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:border-[#7716BC] focus:ring-2 focus:ring-[#7716BC] focus:border-transparent outline-none transition-all duration-300'
          >
            <span className='text-gray-700'>
              Sort by: {sortBy === 'newest' ? 'Newest' : 'Oldest'}
            </span>
            <ChevronDownIcon className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${isSortOpen ? 'rotate-180' : ''}`} />
          </button>

          {isSortOpen && (
            <div className='absolute top-full right-0 mt-1 w-40 bg-white border border-gray-200 rounded-lg shadow-lg z-50'>
              <button
                onClick={() => {
                  setSortBy('newest');
                  setIsSortOpen(false);
                }}
                className={`w-full text-left px-4 py-2 hover:bg-gray-50 transition-colors duration-200 ${sortBy === 'newest' ? 'text-[#7716BC] bg-gray-50' : ''}`}
              >
                Newest
              </button>
              <button
                onClick={() => {
                  setSortBy('oldest');
                  setIsSortOpen(false);
                }}
                className={`w-full text-left px-4 py-2 hover:bg-gray-50 transition-colors duration-200 ${sortBy === 'oldest' ? 'text-[#7716BC] bg-gray-50' : ''}`}
              >
                Oldest
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Blog Grid */}
      {currentBlogs.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {currentBlogs.map((blog, index) => (
            <div key={blog.id || index} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden group">
              <Link href={`/blog/${blog.slug_field}`}>
                <div className="relative overflow-hidden">
                  <Image
                    src={blog.image || "/Images/ML_banner.png"}
                    alt={blog.title}
                    width={400}
                    height={250}
                    className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>
                </div>
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 group-hover:text-[#F245A1] transition-colors duration-300 line-clamp-2">
                    {blog.title}
                  </h3>
                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {truncateText(blog.content)}
                  </p>
                  <div className="flex justify-between items-center">
                    <p className="text-gray-500 text-sm">
                      {formatDate(blog.created_at)}
                    </p>
                    <span className="text-[#F245A1] font-medium text-sm group-hover:underline">
                      Read More
                    </span>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No blogs found matching your criteria.</p>
          <p className="text-gray-400 text-sm mt-2">Try adjusting your search or filters.</p>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex flex-col sm:flex-row justify-center items-center gap-4">
          <div className="flex items-center gap-2">
            <button
              onClick={goToPrevious}
              disabled={currentPage === 1}
              className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
            >
              <ChevronLeftIcon className="w-5 h-5 text-gray-600" />
            </button>

            <div className="flex items-center gap-1">
              {getPageNumbers().map((page, index) => (
                <React.Fragment key={index}>
                  {page === '...' ? (
                    <span className="px-3 py-2 text-gray-400">...</span>
                  ) : (
                    <button
                      onClick={() => goToPage(page)}
                      className={`px-3 py-2 rounded-lg transition-all duration-300 ${
                        currentPage === page
                          ? 'bg-[#7716BC] text-white'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      {page}
                    </button>
                  )}
                </React.Fragment>
              ))}
            </div>

            <button
              onClick={goToNext}
              disabled={currentPage === totalPages}
              className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
            >
              <ChevronRightIcon className="w-5 h-5 text-gray-600" />
            </button>
          </div>

          <p className="text-sm text-gray-500">
            Page {currentPage} of {totalPages}
          </p>
        </div>
      )}
    </div>
  );
};

export default Section4;