import React from 'react'

const Section4 = () => {
  return (
      <div className='container'>
      <div className='flex justify-between'>
              <p>Showing 12 out of 90 results</p>
              <select>
                  <option value="">Sort by</option>
                  <option value="">Newest</option>
                  <option value="">Oldest</option>
              </select>
          </div>
          <div className="flex  items-center gap-3 md:gap-6 my-3 md:my-6">
        <div className=" max-w-sm">
          <div className="rounded-t-lg">
            <img src="/Images/ML_banner.png" alt="Machine learning" />
          </div>
          <div>
            <h3>Transformative Machine Learning Solutions Development</h3>
            <p>May 20, 2025</p>
          </div>

          <div className="flex justify-between items-center">
            <h3 className="text-2xl font-semibold">Featured</h3>
            <div>
              <button>Read More</button>
            </div>
          </div>
        </div>
        <div className=" max-w-sm">
          <div className="rounded-t-lg">
            <img src="/Images/ML_banner.png" alt="Machine learning" />
          </div>
          <div>
            <h3>Transformative Machine Learning Solutions Development</h3>
            <p>May 20, 2025</p>
          </div>

          <div className="flex justify-between items-center">
            <h3 className="text-2xl font-semibold">Featured</h3>
            <div>
              <button>Read More</button>
            </div>
          </div>
        </div>
        <div className=" max-w-sm">
          <div className="rounded-t-lg">
            <img src="/Images/ML_banner.png" alt="Machine learning" />
          </div>
          <div>
            <h3>Transformative Machine Learning Solutions Development</h3>
            <p>May 20, 2025</p>
          </div>

          <div className="flex justify-between items-center">
            <h3 className="text-2xl font-semibold">Featured</h3>
            <div>
              <button>Read More</button>
            </div>
          </div>
        </div>
      </div>
      </div>
  )
}

export default Section4