"use client";

import React, { useState, useEffect } from "react";
import BlogInfoDisplay from "./BlogInfoDisplay";
import BlogsCard from "./BlogInfo";
import { decodeHtmlEntities, truncateText, truncateTitle } from "@/utils/htmlUtils";

const SecondBlogDisplaySection = () => {
  const [blogs, setBlogs] = useState([]);



  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  // Fetch blogs from API
  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        const response = await fetch("https://api.valueans.com/api/blogs/");

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // Get blogs 4-8 (next 5 blogs after the first 3 shown in TopBlogDisplay)
        const blogsData = data.results ? data.results.slice(3, 8) : [];
        setBlogs(blogsData);
      } catch (err) {
        console.error("Error fetching blogs:", err);
        // If API fails, we'll use default data (no need to set error state)
      }
    };

    fetchBlogs();
  }, []);

  // Default blog data for fallback
  const defaultBlogs = [
    {
      id: 1,
      title: "10 Inspiring Reasons to Start an Online Business in 2024",
      image: "/Images/blog2image.png",
      slug_field: "default-blog-1",
      created_at: "2024-01-27",
      content: "The dream of being your boss is a universal aspiration. An online business offers unprecedented freedom and control. You have the liberty to set your own hours, choose your projects, and build a business that aligns with your passions and..."
    },
    {
      id: 2,
      title: "10 Inspiring Reasons to Start an Online Business in 2024",
      image: "/Images/blogcard1.png",
      slug_field: "default-blog-2",
      created_at: "2024-01-27",
      content: "Read about the reasons to start an online business"
    },
    {
      id: 3,
      title: "10 Inspiring Reasons to Start an Online Business in 2024",
      image: "/Images/blogcard2.png",
      slug_field: "default-blog-3",
      created_at: "2024-01-27",
      content: "Read about the reasons to start an online business"
    },
    {
      id: 4,
      title: "10 Inspiring Reasons to Start an Online Business in 2024",
      image: "/Images/blogcard3.png",
      slug_field: "default-blog-4",
      created_at: "2024-01-27",
      content: "Read about the reasons to start an online business"
    },
    {
      id: 5,
      title: "10 Inspiring Reasons to Start an Online Business in 2024",
      image: "/Images/laptops-use.png",
      slug_field: "default-blog-5",
      created_at: "2024-01-27",
      content: "Read about the reasons to start an online business"
    }
  ];

  // Use API data if available, otherwise use default data
  const displayBlogs = blogs.length > 0 ? blogs : defaultBlogs;

  // Get the featured blog (first blog from our slice)
  const featuredBlog = displayBlogs[0];
  // Get the remaining 4 blogs for cards
  const cardBlogs = displayBlogs.slice(1, 5);

  return (
    <section className="flex w-[85vw] md:w-[90vw] mx-auto my-24 justify-center items-center gap-8 flex-col md:flex-row">
      
      {/* BlogInfoDisplay Section */}
      <div className="w-full md:w-1/2">
        <BlogInfoDisplay
          imageSrc={featuredBlog?.image || "/Images/blog2image.png"}
          title={truncateTitle(featuredBlog?.title || "10 Inspiring Reasons to Start an Online Business in 2024")}
          description={truncateText(featuredBlog?.content, 200) || "The dream of being your boss is a universal aspiration. An online business offers unprecedented freedom and control. You have the liberty to set your own hours, choose your projects, and build a business that aligns with your passions and ..."}
          link={`/blog/${featuredBlog?.slug_field}`}
          date={formatDate(featuredBlog?.created_at) || "27 Jan 2021"}
        />
      </div>

      {/* Cards Section */}
      <div className="flex flex-col gap-5 justify-center items-center w-full md:w-1/2 mt-5 md:mt-0">
        {/* Top Row of Cards */}
        <div className="flex justify-start md:items-center gap-5 md:flex-row w-full">
          {cardBlogs.slice(0, 2).map((blog, index) => (
            <BlogsCard
              key={blog.id || index}
              imageSrc={blog.image || (index === 0 ? "/Images/blogcard1.png" : "/Images/blogcard2.png")}
              title={truncateTitle(blog.title || "10 Inspiring Reasons to Start an Online Business in 2024")}
              slug={blog.slug_field}
              date={formatDate(blog.created_at) || "27 Jan 2021"}
            />
          ))}
        </div>
        {/* Bottom Row of Cards */}
        <div className="flex justify-start md:items-center gap-5 md:flex-row w-full">
          {cardBlogs.slice(2, 4).map((blog, index) => (
            <BlogsCard
              key={blog.id || index + 2}
              imageSrc={blog.image || (index === 0 ? "/Images/blogcard3.png" : "/Images/laptops-use.png")}
              title={truncateTitle(blog.title || "10 Inspiring Reasons to Start an Online Business in 2024")}
              slug={blog.slug_field}
              date={formatDate(blog.created_at) || "27 Jan 2021"}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default SecondBlogDisplaySection;
