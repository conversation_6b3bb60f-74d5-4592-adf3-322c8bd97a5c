"use client";

import Image from "next/image";
import React, { useState, useEffect } from "react";
import Link from "next/link";
import { decodeHtmlEntities, truncateText } from "@/utils/htmlUtils";

const TopBlogDisplay = () => {
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);



  // Fetch blogs from API
  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        setLoading(true);
        const response = await fetch("https://api.valueans.com/api/blogs/");

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log("Data:", data);

        // Get first 3 blogs from the results
        const blogsData = data.results ? data.results.slice(0, 3) : [];
        setBlogs(blogsData);
        setError(null);
      } catch (err) {
        console.error("Error fetching blogs:", err);
        setError("Failed to load blogs. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchBlogs();
  }, []);

  // Get the first blog for preview
  const featuredBlog = blogs.length > 0 ? blogs[0] : null;
  // Show loading state
  if (loading) {
    return (
      <div className="max-w-[85vw] mx-auto mt-6 md:mt-12 flex justify-center items-center h-64">
        <div className="text-lg text-gray-600">Loading blogs...</div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="max-w-[85vw] mx-auto mt-6 md:mt-12 flex justify-center items-center h-64">
        <div className="text-lg text-red-600">{error}</div>
      </div>
    );
  }

  // Show empty state
  if (!blogs.length) {
    return (
      <div className="max-w-[85vw] mx-auto mt-6 md:mt-12 flex justify-center items-center h-64">
        <div className="text-lg text-gray-600">No blogs available</div>
      </div>
    );
  }

  return (
    <>
      <div className="max-w-[85vw] mx-auto mt-6 md:mt-12 relative flex flex-col md:flex-row justify-center items-center gap-6 md:gap-10">
        <div className="flex">
          <div
            className="block w-[286px] md:w-[400px] h-[278px] md:h-[300px] px-3 md:px-4 py-3 md:py-6 bg-white border-gray-200 rounded-lg shadow hover:bg-gray-100 absolute z-10 left-[-4px] md:left-[-44px] top-[32px] md:top-[40px] overflow-y-hidden"
            style={{ maxHeight: "300px" }} // Fixed height
          >
            <h1 className="mb-2 text-xl md:text-2xl font-bold tracking-tight text-black">
              {decodeHtmlEntities(featuredBlog?.title || "No title available")}
            </h1>
            <p className="font-normal text-sm md:text-lg text-black">
              {truncateText(featuredBlog?.content, 200) || "No description available"}
            </p>
          </div>

          <div className="w-[339px] md:w-[648px] h-[342px] md:h-[440px] relative">
            <Image
              src={featuredBlog?.image || "/Images/using_laptops.png"}
              alt="Selected Blog Image"
              layout="fill"
              objectFit="cover"
            />
          </div>
        </div>

        <div className="flex-col gap-12">
          {blogs.map((blog, index) => (
            <Link href={`/blog/${blog.slug_field}`} passHref key={blog.id || index}>
              <div className="flex gap-8 mt-5 cursor-pointer">
                <div className="w-[92px] md:w-[115px] h-[86px] md:h-[108px] relative">
                  <Image
                    src={blog.image || "/Images/using_laptops.png"}
                    alt="Blog Thumbnail"
                    layout="fill"
                    objectFit="contain"
                  />
                </div>

                <div className="flex-1">
                  <h4 className="text-lg md:text-xl font-medium leading-{27px} md:leading-[36px]">
                    {decodeHtmlEntities(blog.title)}
                  </h4>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </>
  );
};

export default TopBlogDisplay;
