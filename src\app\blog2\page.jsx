"use client";
import React, { useState } from "react";
import Section1 from "@/Components/BlogRedesign/Section1";
import Section2 from "@/Components/BlogRedesign/Section2";
import Section3 from "@/Components/BlogRedesign/Section3";
import Section4 from "@/Components/BlogRedesign/Section4";

const BlogPage = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState({
    service: "",
    industry: "",
    technology: ""
  });

  // Handle search functionality
  const handleSearch = (term) => {
    setSearchTerm(term);
  };

  // Handle filter changes
  const handleFilterChange = (filterType, value) => {
    if (filterType === 'clear') {
      setFilters({
        service: "",
        industry: "",
        technology: ""
      });
      setSearchTerm("");
    } else {
      setFilters(prev => ({
        ...prev,
        [filterType]: value
      }));
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section with Featured Blog */}
      <Section1 />

      {/* Featured Blogs Carousel */}
      <Section2 />

      {/* Search and Filters */}
      <Section3
        onSearch={handleSearch}
        onFilterChange={handleFilterChange}
        searchTerm={searchTerm}
        filters={filters}
      />

      {/* Blog Grid with Pagination */}
      <Section4
        searchTerm={searchTerm}
        filters={filters}
      />
    </div>
  );
};

export default BlogPage;
