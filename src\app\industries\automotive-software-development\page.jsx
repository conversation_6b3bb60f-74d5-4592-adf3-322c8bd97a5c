import React from "react";
import Section1 from "@/Components/Industries/HealthCare/Section1";
import Section2 from "@/Components/Industries/HealthCare/Section2";
import Section3 from "@/Components/Industries/HealthCare/Section3";
import Section4 from "@/Components/Industries/HealthCare/Section4";
import Section5 from "@/Components/Industries/HealthCare/Section5";
import Section6 from "@/Components/Industries/HealthCare/Section7";
import Section7 from "@/Components/Industries/Logistics/Section7";
import Faq from "@/Components/Faq/Faq";
export const metadata = {
  title: "Automotive industry IT services that improve the user experience",
  description:
    " Valueans provides security integrated automotive solutions along with managed cloud IT services for the automotive industry enhancing cyber security monitoring & management.",
  alternates: {
    canonical: "https://valueans.com/industries/automotive-software-development",
  },
  openGraph: {
    title: "Automotive industry IT services that improve the user experience",
    description: " Valueans provides security integrated automotive solutions along with managed cloud IT services for the automotive industry enhancing cyber security monitoring & management.",
    url: "https://valueans.com/industries/automotive-software-development",
    type: "website",
  },
  images: [
      {
        url: "https://valueans.com/Images/og-automotive.png", // ✅ Must be absolute
        width: 1200,
        height: 630,
        alt: "Automotive Software Development preview",
      },
    ],
};
const page = () => {
  const PinkDotCardData = [
    {
      content: [
        "Encourage ongoing innovation while meeting consumer demands.",
        "Use smart manufacturing to speed up innovation and deliver innovative automotive solutions.",
        "Forecast future developments and make constant adjustments in operations and finance.",
        "Offer cutting-edge IT services for the automotive industry to deliver the greatest possible client experience.",
        "Provide customers with linked digital experiences at any time and from any location.",
        "Minimize downtime when operating your fleet.",
      ],
    },
  ];
  const cardData = [
    {
      title: "Mobility Automotive Technology Solutions",
      content:
        "<a href='/industries/telecom-software-development' class='text-[#7716BC] hover:underline'>Mobility and real time communication</a>– capacity to communicate with clients, staff, assets, goods, and other companies in real time, from any place, is seen by modern business users as a fundamental requirement. They anticipate being paid as promised, being present online, and doing every assignment in a matter of microseconds. Mobimorphosis, also known as <a href='/services/enterprise-mobile-app-development' class='text-[#7716BC] hover:underline'>mobile-centric digital transformation</a>, has emerged as the key driver of workplace mobility. Organizations must adopt a mobile-first-mobile-must approach that promotes collaboration, business enablement, and professional efficiency in order to fully realize the potential of mobile.",
    },
    {
      title: "Data Science as a Service",
      content:
        "A single moment of truth, or stand-alone experience, has the capacity to build or destroy a brand, and social media and technology-enabled transparency have shifted the market dominance from suppliers to consumers. Meeting needs in current time has become essential for success in this fiercely competitive and dynamic industry where consumers can rapidly change their spending habits. <a href='/technologies/predictive-analytics-technologies' class='text-[#7716BC] hover:underline'>Predictive analytics</a> is used in Valueans Data Science consultancy and business analytics platforms to obtain real-time insights and lower customer attrition.",
    },
    {
      title: "Managed Cloud IT Services for Automotive Industry",
      content:
        "If your company wants to develop, deploy, or expand its cloud computing capabilities, Valueans is the ideal cloud partner. With a high degree of analytics and automation, our <a href='/services/managed-cloud-services' class='text-[#7716BC] hover:underline'>managed cloud service</a> gives you access to a hyper-scale cloud environment that can effectively provide governance, accessibility, lifecycle management, and the improvement insights throughout your multi-cloud environment. This allows you to speed up and streamline your operations. We address Application, Infrastructure, and Security in our Cloud Adoption Framework.",
    },
    {
      title: "IT Security Services",
      content:
        "Valueans provides security integrated automotive solutions for IT that tackle the main issues that businesses confront today by using the extensive knowledge of a wide range of seasoned security specialists. Enhancing the agility, adaptability, and cost-effectiveness of the next generation's information safety and compliance programs is the goal of our IT services for the automotive industry. With our automotive technology solutions in access and identification administration, data protection, risk management and mitigation (application, network, and mobile), and cyber security monitoring & management, we guarantee a comprehensive risk-driven strategy for businesses.",
    },
    {
      title: "Managed Infrastructure and Security Services",
      content:
        "With the help of our combined commercial, technological, and industry skills, we provide managed security services and unique managed infrastructure services utilizing cloud and conventional technologies. Infrastructure safety measures and managed security IT services for the automotive industry are not combined into a comprehensive solution by our managed security service providers. Customers may save support expenses and gain a better understanding of the IT infrastructure's performance with Valueans Managed Infrastructure Services.",
    },
    {
      title: "Product Engineering Services",
      content:
        "As technology continues to progress, businesses have altered their business practices. Valueans' constant goal is to provide businesses a competitive edge by using new technologies to manage scalability, spur innovation, and penetrate new markets. We can help you accelerate company transformation by providing a smart, secure, and connected experience spanning <a href='/services/full-stack-development-services' class='text-[#7716BC] hover:underline'>Enterprise Platforms</a>, Data Center Technologies, and the Internet of Things through a constant access paradigm, all while concentrating exclusively on Mobile, Cloud, and Social technologies.",
    },
  ];
  const PinkDotCardData2 = [
    {
      title: "Shorten 'code to road' schedules:",
      feature:
        "To thrive in the market for software-defined vehicles, speed up automobile industry software development and deploy new in-car capabilities from 'code to road' as quickly as feasible.",
    },
    {
      title: "Reach a level of versatility never before possible: ",
      feature:
        "By combining AI with open source technologies, applications and AI models can be developed and tested in the backend before being readily deployed into the car.",
    },

    {
      title: "Make user experiences more intelligent:",
      feature:
        "Recognize the demands of your clients and expedite the creation of tailored, linked experiences while adhering to AI transparency regulations.",
    },
    {
      title: "Enhance engineering efforts:",
      feature:
        "Boost engineers' efficiency and speed to enable faster time to market and better quality with well-thought-out integrated automotive solutions and reliable AI.",
    },
    {
      title: "Advance the development of autonomous driving:",
      feature:
        "Manage massive volumes of vehicle data effectively by putting in place a hybrid engineering data management system.",
    },
    {
      title:
        "Use cybersecurity to increase the value of your products and brand:",
      feature:
        "Integrate end-to-end data security into the ecosystem of connected, autonomous, shared, and electric vehicles as well as throughout the product life cycle.",
    },
  ];
  const PinkTopCardData = [
    {
      title: "Embedded Systems",
      description:
        "From basic electrical components in tiny gadgets to sophisticated systems in large equipment, embedded systems are widely used in a variety of applications. <a href='/services/enterprise-software-development' class='text-[#7716BC] hover:underline'>Embedded technologies</a> are at the core of many contemporary technological gadgets. Time-sensitive equipment or systems that need a highly dependable system to guarantee actions are carried out when needed are the most common applications for these systems. Valueans assists our clients in creating some of the most intricate embedded computer innovative automotive solutions by utilizing its multidomain, multi industry embedded systems experience.",
    },
    {
      title: "Digital Engineering",
      description:
        "The future frontiers of progress are being driven by new developments in <a href='/services/data-engineering-solutions' class='text-[#7716BC] hover:underline'>data processing, data storage</a>, and communications technologies in our digital age. Continued advancements in the digital, physical, and virtual domains are changing how products are created, produced, and manufactured, while the continued growth of innovative technologies continues to enhance worldwide engineering automotive industry IT services. The new generation of integrated automotive solutions and products of businesses that can take advantage of the capabilities released by digital engineering will thus determine the future as the globe changes and demand patterns mature.",
    },
    {
      title: "Digital Manufacturing Transformation",
      description:
        "We think that manufacturing is undergoing a tectonic shift right now, and that adapting to this change will require digital manufacturing transformation. Today, the world is undergoing a tectonic shift, with manufacturing at its core and playing a key role in promoting sustainability. The new paradigm asks for balancing business survival and preparing the industrial workforce for the new environment. There is a need to be adaptable while working on the forefront and planning for the years to come at the same time.",
    },
    
  ];
  const CardData2 = [
    {
      title: "1. Real-Time Inventory Control",
      description:
        "Handle product materials in one location and keep an eye on committed, expected, and on-hand inventory levels in real-time.",
    },
    {
      title: "2. Complete Traceability",
      description:
        "Generate your internal and batch barcodes automatically, and quickly find every manufactured item in several warehouses for complete traceability. ",
    },
    {
      title: "3. Management of Purchase Orders",
      description:
        "Purchase sales quantities may be automatically converted to whatever currency you choose by selecting and using various currencies.",
    },
    {
      title: "4. Create Your Own Processes",
      description:
        "Using our automobile industry software's native connectors and open API, you can integrate your preferred tools and establish workflows to optimize your business processes.",
    },
    {
      title: "5. Precise Pricing",
      description:
        "Utilize the <a href='/industries/manufacturing-software-development' class='text-[#7716BC] hover:underline'>auto generated bill of materials (BOM)</a> and different production procedures to keep tabs on your manufacturing expenses.",
    },
    {
      title: "6. Planning for Production",
      description:
        "Drag and drop manufacturing job priority to obtain precise operation completion dates. ",
    },
    {
      title: "7. Master Planning in Real Time",
      description:
        "Get real-time visibility to optimize inventory levels consistently and simplify all of your important resources.",
    },
    {
      title: "8. Order Handling Across Many Channels",
      description:
        "For efficient order fulfillment, integrate your <a href='/industries/ecommerce-app-development' class='text-[#7716BC] hover:underline'>e-commerce</a> and business-to-business order management systems and align your sales channels.",
    },
    {
      title: "9. Strong Shop Floor Management",
      description:
        "Track the lead time for production jobs and view the manufacturing order task for each activity.",
    },
  ];
  const accordionData = [
    {
      title: "What is software development for automobiles?",
      content:
        "The field of designing and creating software for the automotive industry and integrated automotive solutions for use in automotive technology is known as automotive software engineering.",
    },
    {
      title: "How much does it cost to build software for cars?",
      content:
        "Depending on complexity, the cost of developing automobile industry software during this stage ranges from $40,000 to $150,000. Writing optimal code, connecting software for the automotive industry with hardware, such as sensors or ECUs, and doing thorough testing are important tasks.",
    },
    {
      title: "The automobile industry uses which programming language?",
      content:
        "Because C and C++ are the best languages for embedded systems with limited resources, automobile industry software is typically created in these languages.",
    },
    {
      title: "Why is software for automobiles important?",
      content:
        "Infotainment systems, which enhance ride quality, are a significant application of automotive software development. They integrate music, video, internet access, and navigation into one platform. This facilitates the use of information, entertainment, and communication services by both drivers and passengers.",
    },
    {
      title: "How long would it take a business to put an ERP solution into place?",
      content:
        "The scope and level of detail of your company might affect how long it takes to establish an ERP solution. It also relies on the level of customisation required to maximize your company's potential. ERPs may be implemented in a few days or a week by smaller businesses. Typically, mid-sized businesses need a few weeks or longer.",
    },
  ];
  return (
    <div>
      <Section1
        backgroundImage={"/Images/Automotive-bg.jpg"}
        heading={"Automotive industry iT services"}
        bannerText={
          "Leveraging Automotive Industry With A Sustainable Ecosystem"
        }
      />
      <Section2
        heading={"Valueans Automotive Technology Solutions"}
        paragraph={
          "With automotive industry IT services that improve the customer experience and draw in and keep top personnel, we help you propel digital evolution and growth so your company may develop more quickly. Integrated automotive solutions to get your vehicles moving quickly. "
        }
      />
      <Section3
        PinkDotCardData={PinkDotCardData}
        heading={"The Most Innovative Automotive Solutions"}
        image={"/Images/Automotive2.png"}
        paragraph={"With our automotive industry IT services, we can help you:"}
      />
      <Section4
        cardData={cardData}
        spanHeading={"IT Services for Automotive Industry"}
      />
      <Section5
        PinkDotCardData={PinkDotCardData2}
        headingLeft={"Why Choose Valueans Automotive Industry IT Services"}
        image={"/Images/Automotive3.png"}
        classname={"md:flex-row-reverse"}
      />
      <Section6
        PinkTopCardData={PinkTopCardData}
        Cardheight={"md:h-[420px]"}
        heading={"Leveraging New Automotive Value Paradigms"}
        gridClass={"md:grid-cols-2"}
        cardHeaderHeight={"h-[50px] "}
      />
      <Section7
        cardData={CardData2}
        heading={"Why Choose Valueans for Automotive Software Solutions?"}
      />
      <Faq content={accordionData} />
    </div>
  );
};

export default page;
