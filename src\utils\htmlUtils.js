/**
 * Utility functions for handling HTML content and entities
 */

/**
 * Safely decode HTML entities using DOM API (client-side) or manual decoding (server-side)
 * @param {string} text - Text containing HTML entities
 * @returns {string} - Decoded text
 */
export const decodeHtmlEntities = (text) => {
  if (!text) return "";

  // Check if we're in a browser environment
  if (typeof window !== 'undefined' && typeof document !== 'undefined') {
    // Client-side: Use DOM API for accurate decoding
    const textarea = document.createElement('textarea');
    textarea.innerHTML = text;
    return textarea.value;
  } else {
    // Server-side: Use manual decoding for common HTML entities
    return text
      .replace(/&#39;/g, "'")
      .replace(/&quot;/g, '"')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&nbsp;/g, ' ')
      .replace(/&#x27;/g, "'")
      .replace(/&#x2F;/g, '/')
      .replace(/&#x60;/g, '`')
      .replace(/&#x3D;/g, '=');
  }
};

/**
 * Clean HTML content and extract meaningful text
 * @param {string} html - HTML content to clean
 * @returns {string} - Cleaned plain text
 */
export const cleanHtmlContent = (html) => {
  if (!html) return "";

  // Remove CSS styles (everything between <style> tags)
  let cleaned = html.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, "");

  // Remove script tags
  cleaned = cleaned.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, "");

  // Remove HTML comments
  cleaned = cleaned.replace(/<!--[\s\S]*?-->/g, "");

  // Remove all HTML tags
  cleaned = cleaned.replace(/<[^>]*>/g, "");

  // Remove CSS rules that might be inline (like body { ... })
  cleaned = cleaned.replace(/[a-zA-Z-]+\s*\{[^}]*\}/g, "");

  // Remove extra whitespace and line breaks
  cleaned = cleaned.replace(/\s+/g, " ").trim();

  // Remove any remaining CSS-like syntax
  cleaned = cleaned.replace(/[a-zA-Z-]+:\s*[^;]+;/g, "");

  // Decode HTML entities (like &#39; to ')
  cleaned = decodeHtmlEntities(cleaned);

  return cleaned;
};

/**
 * Truncate text to specified length
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum length
 * @returns {string} - Truncated text
 */
export const truncateText = (text, maxLength = 200) => {
  if (!text) return "";
  const plainText = cleanHtmlContent(text);
  if (plainText.length <= maxLength) return plainText;
  return plainText.substring(0, maxLength) + "...";
};

/**
 * Truncate title with HTML entity decoding
 * @param {string} title - Title to truncate
 * @param {number} maxLength - Maximum length
 * @returns {string} - Truncated and decoded title
 */
export const truncateTitle = (title, maxLength = 60) => {
  if (!title) return "";
  const decodedTitle = decodeHtmlEntities(title);
  if (decodedTitle.length <= maxLength) return decodedTitle;
  return decodedTitle.substring(0, maxLength) + "...";
};
